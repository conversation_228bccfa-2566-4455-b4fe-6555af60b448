<?php return array('a11y.js' => array('dependencies' => array('wp-dom-ready', 'wp-i18n'), 'version' => '55ca8e5ef2be0319312c'), 'annotations.js' => array('dependencies' => array('wp-data', 'wp-hooks', 'wp-i18n', 'wp-rich-text'), 'version' => '471e036ab9d70fb8aae4'), 'api-fetch.js' => array('dependencies' => array('wp-i18n', 'wp-url'), 'version' => 'c7fab2b80f62e49268dd'), 'autop.js' => array('dependencies' => array(), 'version' => 'a0567e518e6863383ace'), 'blob.js' => array('dependencies' => array('wp-polyfill'), 'version' => '65d38acd0a443932b695'), 'block-directory.js' => array('dependencies' => array('react-jsx-runtime', 'wp-a11y', 'wp-api-fetch', 'wp-block-editor', 'wp-blocks', 'wp-components', 'wp-compose', 'wp-core-data', 'wp-data', 'wp-editor', 'wp-element', 'wp-hooks', 'wp-html-entities', 'wp-i18n', 'wp-notices', 'wp-plugins', 'wp-primitives', 'wp-url'), 'version' => 'a1664d89168bc2485dcd'), 'block-editor.js' => array('dependencies' => array('react', 'react-dom', 'react-jsx-runtime', 'wp-a11y', 'wp-api-fetch', 'wp-blob', 'wp-block-serialization-default-parser', 'wp-blocks', 'wp-commands', 'wp-components', 'wp-compose', 'wp-data', 'wp-date', 'wp-deprecated', 'wp-dom', 'wp-element', 'wp-hooks', 'wp-html-entities', 'wp-i18n', 'wp-is-shallow-equal', 'wp-keyboard-shortcuts', 'wp-keycodes', 'wp-notices', 'wp-polyfill', 'wp-preferences', 'wp-primitives', 'wp-private-apis', 'wp-rich-text', 'wp-style-engine', 'wp-token-list', 'wp-url', 'wp-warning', 'wp-wordcount'), 'version' => 'b0bb799aac3091ff219f'), 'block-library.js' => array('dependencies' => array('react-jsx-runtime', 'wp-a11y', 'wp-api-fetch', 'wp-autop', 'wp-blob', 'wp-block-editor', 'wp-blocks', 'wp-components', 'wp-compose', 'wp-core-data', 'wp-data', 'wp-date', 'wp-deprecated', 'wp-dom', 'wp-element', 'wp-escape-html', 'wp-hooks', 'wp-html-entities', 'wp-i18n', 'wp-keyboard-shortcuts', 'wp-keycodes', 'wp-notices', 'wp-patterns', 'wp-polyfill', 'wp-primitives', 'wp-private-apis', 'wp-rich-text', 'wp-server-side-render', 'wp-url', 'wp-wordcount'), 'version' => 'ce47f7f842390f9eee0e'), 'block-serialization-default-parser.js' => array('dependencies' => array(), 'version' => '1d1bef54e84a98f3efb9'), 'blocks.js' => array('dependencies' => array('react-jsx-runtime', 'wp-autop', 'wp-blob', 'wp-block-serialization-default-parser', 'wp-data', 'wp-deprecated', 'wp-dom', 'wp-element', 'wp-hooks', 'wp-html-entities', 'wp-i18n', 'wp-is-shallow-equal', 'wp-polyfill', 'wp-private-apis', 'wp-rich-text', 'wp-shortcode', 'wp-warning'), 'version' => '6a8234334dbdc1fec346'), 'commands.js' => array('dependencies' => array('react', 'react-dom', 'react-jsx-runtime', 'wp-components', 'wp-data', 'wp-element', 'wp-i18n', 'wp-keyboard-shortcuts', 'wp-primitives', 'wp-private-apis'), 'version' => 'e88200cd2286cc7dc5c5'), 'components.js' => array('dependencies' => array('react', 'react-dom', 'react-jsx-runtime', 'wp-a11y', 'wp-compose', 'wp-date', 'wp-deprecated', 'wp-dom', 'wp-element', 'wp-escape-html', 'wp-hooks', 'wp-html-entities', 'wp-i18n', 'wp-is-shallow-equal', 'wp-keycodes', 'wp-polyfill', 'wp-primitives', 'wp-private-apis', 'wp-rich-text', 'wp-warning'), 'version' => '668de14d1acfcfd5933e'), 'compose.js' => array('dependencies' => array('react', 'react-jsx-runtime', 'wp-deprecated', 'wp-dom', 'wp-element', 'wp-is-shallow-equal', 'wp-keycodes', 'wp-polyfill', 'wp-priority-queue'), 'version' => 'aeb36c3a0446831eb804'), 'core-commands.js' => array('dependencies' => array('react-jsx-runtime', 'wp-commands', 'wp-compose', 'wp-core-data', 'wp-data', 'wp-element', 'wp-html-entities', 'wp-i18n', 'wp-notices', 'wp-primitives', 'wp-private-apis', 'wp-router', 'wp-url'), 'version' => '1b912ba4c68267d8cd07'), 'core-data.js' => array('dependencies' => array('react-jsx-runtime', 'wp-api-fetch', 'wp-block-editor', 'wp-blocks', 'wp-compose', 'wp-data', 'wp-deprecated', 'wp-element', 'wp-html-entities', 'wp-i18n', 'wp-is-shallow-equal', 'wp-polyfill', 'wp-private-apis', 'wp-rich-text', 'wp-url', 'wp-warning'), 'version' => '644510279a0299f222b5'), 'customize-widgets.js' => array('dependencies' => array('react-jsx-runtime', 'wp-block-editor', 'wp-block-library', 'wp-blocks', 'wp-components', 'wp-compose', 'wp-core-data', 'wp-data', 'wp-dom', 'wp-element', 'wp-hooks', 'wp-i18n', 'wp-is-shallow-equal', 'wp-keyboard-shortcuts', 'wp-keycodes', 'wp-media-utils', 'wp-polyfill', 'wp-preferences', 'wp-primitives', 'wp-private-apis', 'wp-widgets'), 'version' => 'e1dacbb2da4019844b19'), 'data.js' => array('dependencies' => array('react-jsx-runtime', 'wp-compose', 'wp-deprecated', 'wp-element', 'wp-is-shallow-equal', 'wp-polyfill', 'wp-priority-queue', 'wp-private-apis', 'wp-redux-routine'), 'version' => 'b14ce5fd6799177c7022'), 'data-controls.js' => array('dependencies' => array('wp-api-fetch', 'wp-data', 'wp-deprecated'), 'version' => '06af18ace9a4aeb126e9'), 'date.js' => array('dependencies' => array('moment', 'wp-deprecated'), 'version' => 'dd508f9121b7db4da62d'), 'deprecated.js' => array('dependencies' => array('wp-hooks'), 'version' => '1aa7a2722e5853bb3a37'), 'dom.js' => array('dependencies' => array('wp-deprecated'), 'version' => 'fe8367099d12064e2c18'), 'dom-ready.js' => array('dependencies' => array(), 'version' => '5b9fa8df0892dc9a7c41'), 'edit-post.js' => array('dependencies' => array('react-jsx-runtime', 'wp-api-fetch', 'wp-block-editor', 'wp-block-library', 'wp-blocks', 'wp-commands', 'wp-components', 'wp-compose', 'wp-core-commands', 'wp-core-data', 'wp-data', 'wp-deprecated', 'wp-editor', 'wp-element', 'wp-hooks', 'wp-html-entities', 'wp-i18n', 'wp-keyboard-shortcuts', 'wp-keycodes', 'wp-notices', 'wp-plugins', 'wp-preferences', 'wp-primitives', 'wp-private-apis', 'wp-url', 'wp-widgets'), 'version' => '154fdf08612b19e91e18'), 'edit-site.js' => array('dependencies' => array('react', 'react-dom', 'react-jsx-runtime', 'wp-a11y', 'wp-api-fetch', 'wp-blob', 'wp-block-editor', 'wp-block-library', 'wp-blocks', 'wp-commands', 'wp-components', 'wp-compose', 'wp-core-commands', 'wp-core-data', 'wp-data', 'wp-date', 'wp-deprecated', 'wp-dom', 'wp-editor', 'wp-element', 'wp-hooks', 'wp-html-entities', 'wp-i18n', 'wp-keyboard-shortcuts', 'wp-keycodes', 'wp-notices', 'wp-patterns', 'wp-plugins', 'wp-polyfill', 'wp-preferences', 'wp-primitives', 'wp-priority-queue', 'wp-private-apis', 'wp-router', 'wp-url', 'wp-warning', 'wp-widgets'), 'version' => 'd73c63851e1620c7658c'), 'edit-widgets.js' => array('dependencies' => array('react-jsx-runtime', 'wp-api-fetch', 'wp-block-editor', 'wp-block-library', 'wp-blocks', 'wp-components', 'wp-compose', 'wp-core-data', 'wp-data', 'wp-deprecated', 'wp-dom', 'wp-element', 'wp-hooks', 'wp-i18n', 'wp-keyboard-shortcuts', 'wp-keycodes', 'wp-media-utils', 'wp-notices', 'wp-patterns', 'wp-plugins', 'wp-preferences', 'wp-primitives', 'wp-private-apis', 'wp-url', 'wp-viewport', 'wp-widgets'), 'version' => '7807e48a6b8d48818b1d'), 'editor.js' => array('dependencies' => array('react', 'react-jsx-runtime', 'wp-a11y', 'wp-api-fetch', 'wp-blob', 'wp-block-editor', 'wp-blocks', 'wp-commands', 'wp-components', 'wp-compose', 'wp-core-data', 'wp-data', 'wp-date', 'wp-deprecated', 'wp-dom', 'wp-element', 'wp-hooks', 'wp-html-entities', 'wp-i18n', 'wp-keyboard-shortcuts', 'wp-keycodes', 'wp-media-utils', 'wp-notices', 'wp-patterns', 'wp-plugins', 'wp-polyfill', 'wp-preferences', 'wp-primitives', 'wp-private-apis', 'wp-rich-text', 'wp-server-side-render', 'wp-url', 'wp-viewport', 'wp-warning', 'wp-wordcount'), 'version' => '1a96749740275ab0fa5c'), 'element.js' => array('dependencies' => array('react', 'react-dom', 'wp-escape-html'), 'version' => 'e52342dc5bff5db05202'), 'escape-html.js' => array('dependencies' => array(), 'version' => '3fb34b7f013aaa921ca4'), 'fields.js' => array('dependencies' => array('react-jsx-runtime', 'wp-blob', 'wp-components', 'wp-core-data', 'wp-data', 'wp-element', 'wp-html-entities', 'wp-i18n', 'wp-notices', 'wp-patterns', 'wp-polyfill', 'wp-primitives', 'wp-private-apis', 'wp-url'), 'version' => 'a7cd9e1f98d5d4a713fc'), 'format-library.js' => array('dependencies' => array('react-jsx-runtime', 'wp-a11y', 'wp-block-editor', 'wp-components', 'wp-data', 'wp-element', 'wp-html-entities', 'wp-i18n', 'wp-primitives', 'wp-private-apis', 'wp-rich-text', 'wp-url'), 'version' => '42c910dbadcb45809f36'), 'hooks.js' => array('dependencies' => array(), 'version' => '5b4ec27a7b82f601224a'), 'html-entities.js' => array('dependencies' => array(), 'version' => '0d1913e5b8fb9137bad2'), 'i18n.js' => array('dependencies' => array('wp-hooks'), 'version' => '2aff907006e2aa00e26e'), 'is-shallow-equal.js' => array('dependencies' => array(), 'version' => 'e70dad7478a6d81b381b'), 'keyboard-shortcuts.js' => array('dependencies' => array('react-jsx-runtime', 'wp-data', 'wp-element', 'wp-keycodes', 'wp-polyfill'), 'version' => 'eebfd8a1739cacabed58'), 'keycodes.js' => array('dependencies' => array('wp-i18n'), 'version' => 'bdac64cae9b64d2585cf'), 'list-reusable-blocks.js' => array('dependencies' => array('react-jsx-runtime', 'wp-api-fetch', 'wp-blob', 'wp-components', 'wp-compose', 'wp-element', 'wp-i18n'), 'version' => '407c3e12ac73b9cbee9e'), 'media-utils.js' => array('dependencies' => array('wp-api-fetch', 'wp-blob', 'wp-element', 'wp-i18n'), 'version' => '6752337227479ce68d71'), 'notices.js' => array('dependencies' => array('wp-data'), 'version' => 'bb4dbe982e6a0739f30e'), 'nux.js' => array('dependencies' => array('react-jsx-runtime', 'wp-components', 'wp-compose', 'wp-data', 'wp-deprecated', 'wp-element', 'wp-i18n', 'wp-primitives'), 'version' => '0d352829dae6be46286c'), 'patterns.js' => array('dependencies' => array('react-jsx-runtime', 'wp-a11y', 'wp-block-editor', 'wp-blocks', 'wp-components', 'wp-compose', 'wp-core-data', 'wp-data', 'wp-element', 'wp-html-entities', 'wp-i18n', 'wp-notices', 'wp-polyfill', 'wp-primitives', 'wp-private-apis', 'wp-url'), 'version' => 'cccd847dbe8005fceb0d'), 'plugins.js' => array('dependencies' => array('react-jsx-runtime', 'wp-compose', 'wp-element', 'wp-hooks', 'wp-is-shallow-equal', 'wp-primitives'), 'version' => 'cd5358b6369eff6f7b85'), 'preferences.js' => array('dependencies' => array('react-jsx-runtime', 'wp-a11y', 'wp-components', 'wp-compose', 'wp-data', 'wp-deprecated', 'wp-element', 'wp-i18n', 'wp-primitives', 'wp-private-apis'), 'version' => 'da016598bccd137bef05'), 'preferences-persistence.js' => array('dependencies' => array('wp-api-fetch'), 'version' => '2a0eed407631381ede85'), 'primitives.js' => array('dependencies' => array('react-jsx-runtime', 'wp-element'), 'version' => 'b0b42d3e72e5c4ecd741'), 'priority-queue.js' => array('dependencies' => array(), 'version' => '0ac29e2c7d9453425a64'), 'private-apis.js' => array('dependencies' => array(), 'version' => '90a811220a86719cf87e'), 'redux-routine.js' => array('dependencies' => array(), 'version' => '5f3792bc38d48a9d5db7'), 'reusable-blocks.js' => array('dependencies' => array('react-jsx-runtime', 'wp-block-editor', 'wp-blocks', 'wp-components', 'wp-core-data', 'wp-data', 'wp-element', 'wp-i18n', 'wp-notices', 'wp-primitives', 'wp-url'), 'version' => '0398e0f8154bcab2d593'), 'rich-text.js' => array('dependencies' => array('wp-a11y', 'wp-compose', 'wp-data', 'wp-deprecated', 'wp-element', 'wp-escape-html', 'wp-i18n', 'wp-keycodes', 'wp-polyfill'), 'version' => '5baa4898706ad54414c7'), 'router.js' => array('dependencies' => array('react-jsx-runtime', 'wp-element', 'wp-polyfill', 'wp-private-apis', 'wp-url'), 'version' => 'e81d01009da0eb5de3a5'), 'server-side-render.js' => array('dependencies' => array('react-jsx-runtime', 'wp-api-fetch', 'wp-blocks', 'wp-components', 'wp-compose', 'wp-data', 'wp-element', 'wp-i18n', 'wp-url'), 'version' => '3dce81021856a751c1dd'), 'shortcode.js' => array('dependencies' => array(), 'version' => '577c74513f927a05a979'), 'style-engine.js' => array('dependencies' => array(), 'version' => '2669e79b844723f88b51'), 'token-list.js' => array('dependencies' => array(), 'version' => '09fdc83606f766278b8b'), 'undo-manager.js' => array('dependencies' => array('wp-is-shallow-equal'), 'version' => '131d70acce3788278416'), 'url.js' => array('dependencies' => array('wp-polyfill'), 'version' => 'd62dba05ffc50c672f4a'), 'viewport.js' => array('dependencies' => array('react-jsx-runtime', 'wp-compose', 'wp-data'), 'version' => 'c65a5cb114e86d027c76'), 'warning.js' => array('dependencies' => array(), 'version' => '58eb333ec38c8d28ba1d'), 'widgets.js' => array('dependencies' => array('react-jsx-runtime', 'wp-api-fetch', 'wp-block-editor', 'wp-blocks', 'wp-components', 'wp-compose', 'wp-core-data', 'wp-data', 'wp-element', 'wp-i18n', 'wp-notices', 'wp-polyfill', 'wp-primitives'), 'version' => '55092f5b53fc73c98299'), 'wordcount.js' => array('dependencies' => array(), 'version' => 'c67f865e3ce4abde9fdb'));
