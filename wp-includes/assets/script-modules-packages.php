<?php return array('interactivity/index.js' => array('dependencies' => array('wp-polyfill'), 'version' => '6434abc611f955a997fe', 'type' => 'module'), 'interactivity/debug.js' => array('dependencies' => array('wp-polyfill'), 'version' => 'd1aa3776a8a1b1588997', 'type' => 'module'), 'interactivity-router/index.js' => array('dependencies' => array('@wordpress/interactivity', array('id' => '@wordpress/a11y', 'import' => 'dynamic')), 'version' => '4b36f376cc3aab08acca', 'type' => 'module'), 'a11y/index.js' => array('dependencies' => array(), 'version' => 'b3a7f46c0ef4f3484886', 'type' => 'module'), 'block-library/file/view.js' => array('dependencies' => array('@wordpress/interactivity'), 'version' => 'b0cd471b6fde34702d88', 'type' => 'module'), 'block-library/image/view.js' => array('dependencies' => array('@wordpress/interactivity'), 'version' => 'e1ce544dd878f3a09f70', 'type' => 'module'), 'block-library/navigation/view.js' => array('dependencies' => array('@wordpress/interactivity'), 'version' => '9510985aedc1f8e088f3', 'type' => 'module'), 'block-library/query/view.js' => array('dependencies' => array('@wordpress/interactivity', array('id' => '@wordpress/interactivity-router', 'import' => 'dynamic')), 'version' => '8e6f28f734f3c306b648', 'type' => 'module'), 'block-library/search/view.js' => array('dependencies' => array('@wordpress/interactivity'), 'version' => 'acdb7febda1392ad28de', 'type' => 'module'));
