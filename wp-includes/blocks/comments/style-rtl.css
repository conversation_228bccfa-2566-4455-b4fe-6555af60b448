.wp-block-post-comments{
  box-sizing:border-box;
}
.wp-block-post-comments .alignleft{
  float:right;
}
.wp-block-post-comments .alignright{
  float:left;
}
.wp-block-post-comments .navigation:after{
  clear:both;
  content:"";
  display:table;
}
.wp-block-post-comments .commentlist{
  clear:both;
  list-style:none;
  margin:0;
  padding:0;
}
.wp-block-post-comments .commentlist .comment{
  min-height:2.25em;
  padding-right:3.25em;
}
.wp-block-post-comments .commentlist .comment p{
  font-size:1em;
  line-height:1.8;
  margin:1em 0;
}
.wp-block-post-comments .commentlist .children{
  list-style:none;
  margin:0;
  padding:0;
}
.wp-block-post-comments .comment-author{
  line-height:1.5;
}
.wp-block-post-comments .comment-author .avatar{
  border-radius:1.5em;
  display:block;
  float:right;
  height:2.5em;
  margin-left:.75em;
  margin-top:.5em;
  width:2.5em;
}
.wp-block-post-comments .comment-author cite{
  font-style:normal;
}
.wp-block-post-comments .comment-meta{
  font-size:.875em;
  line-height:1.5;
}
.wp-block-post-comments .comment-meta b{
  font-weight:400;
}
.wp-block-post-comments .comment-meta .comment-awaiting-moderation{
  display:block;
  margin-bottom:1em;
  margin-top:1em;
}
.wp-block-post-comments .comment-body .commentmetadata{
  font-size:.875em;
}
.wp-block-post-comments .comment-form-author label,.wp-block-post-comments .comment-form-comment label,.wp-block-post-comments .comment-form-email label,.wp-block-post-comments .comment-form-url label{
  display:block;
  margin-bottom:.25em;
}
.wp-block-post-comments .comment-form input:not([type=submit]):not([type=checkbox]),.wp-block-post-comments .comment-form textarea{
  box-sizing:border-box;
  display:block;
  width:100%;
}
.wp-block-post-comments .comment-form-cookies-consent{
  display:flex;
  gap:.25em;
}
.wp-block-post-comments .comment-form-cookies-consent #wp-comment-cookies-consent{
  margin-top:.35em;
}
.wp-block-post-comments .comment-reply-title{
  margin-bottom:0;
}
.wp-block-post-comments .comment-reply-title :where(small){
  font-size:var(--wp--preset--font-size--medium, smaller);
  margin-right:.5em;
}
.wp-block-post-comments .reply{
  font-size:.875em;
  margin-bottom:1.4em;
}
.wp-block-post-comments input:not([type=submit]),.wp-block-post-comments textarea{
  border:1px solid #949494;
  font-family:inherit;
  font-size:1em;
}
.wp-block-post-comments input:not([type=submit]):not([type=checkbox]),.wp-block-post-comments textarea{
  padding:calc(.667em + 2px);
}

:where(.wp-block-post-comments input[type=submit]){
  border:none;
}