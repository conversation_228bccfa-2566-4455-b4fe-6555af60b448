{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "core/columns", "title": "Columns", "category": "design", "allowedBlocks": ["core/column"], "description": "Display content in multiple columns, with blocks added to each column.", "textdomain": "default", "attributes": {"verticalAlignment": {"type": "string"}, "isStackedOnMobile": {"type": "boolean", "default": true}, "templateLock": {"type": ["string", "boolean"], "enum": ["all", "insert", "contentOnly", false]}}, "supports": {"anchor": true, "align": ["wide", "full"], "html": false, "color": {"gradients": true, "link": true, "heading": true, "button": true, "__experimentalDefaultControls": {"background": true, "text": true}}, "spacing": {"blockGap": {"__experimentalDefault": "2em", "sides": ["horizontal", "vertical"]}, "margin": ["top", "bottom"], "padding": true, "__experimentalDefaultControls": {"padding": true, "blockGap": true}}, "layout": {"allowSwitching": false, "allowInheriting": false, "allowEditing": false, "default": {"type": "flex", "flexWrap": "nowrap"}}, "__experimentalBorder": {"color": true, "radius": true, "style": true, "width": true, "__experimentalDefaultControls": {"color": true, "radius": true, "style": true, "width": true}}, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontFamily": true, "__experimentalFontWeight": true, "__experimentalFontStyle": true, "__experimentalTextTransform": true, "__experimentalTextDecoration": true, "__experimentalLetterSpacing": true, "__experimentalDefaultControls": {"fontSize": true}}, "interactivity": {"clientNavigation": true}, "shadow": true}, "editorStyle": "wp-block-columns-editor", "style": "wp-block-columns"}