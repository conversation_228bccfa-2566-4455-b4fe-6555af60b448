<?php
/**
 * The template for displaying header.
 *
 * @package HelloElementor
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}


if ( ! hello_get_header_display() ) {
	return;
}

$is_editor = isset( $_GET['elementor-preview'] );
$site_name = get_bloginfo( 'name' );
$tagline   = get_bloginfo( 'description', 'display' );
$header_nav_menu = wp_nav_menu( [
	'theme_location' => 'menu-1',
	'fallback_cb' => false,
	'echo' => false,
] );
?>

<?php
//<editor-fold desc="Dynnamic Menu">
$language = 'GB';
if ( defined( 'ICL_LANGUAGE_CODE' ) ) {
	$language = strtoupper( ICL_LANGUAGE_CODE );
}

$show_lang_selector = true;//$language != 'IT';
$environment        = "prod";
if ( defined( 'KINSTA_DEV_ENV' ) && KINSTA_DEV_ENV == true ) {
	$environment = "qa";
}
?>

<?php if ( true/*visualcomposerstarter_is_the_header_displayed()*/ ) : ?>
<?php //visualcomposerstarter_hook_before_header(); ?>
<header id="header" class="site-header dynamic-header <?php echo esc_attr( hello_get_header_layout_class() ); ?>"
    role="banner">
    <div class="container">
        <?php

			if(function_exists('get_field')){
				if(get_field('menu_top_bar', 'option')){
					include 'menu-parts/top-header-bar.php';
				}
			}
			?>

        <div class="icon-top-bar w-100 d-flex align-items-center justify-content-between px-4">
            <div class="icon-wrapper w-100 h-100">
                <a href="https://kansspelautoriteit.nl/veilig-spelen/veilig-online-gokken/">
                    <svg xmlns="http://www.w3.org/2000/svg" width="58" height="18" fill="none">
                        <g fill="#fff" clip-path="url(#ksa-logo_svg__a)">
                            <path
                                d="m2.577 7.422-1.8-4.586h.559l1.463 3.782 1.435-3.76.54-.022h.048L3.044 7.422zM6.537 7.486a1.59 1.59 0 0 1-1.116-.497 1.63 1.63 0 0 1-.444-1.151c0-.892.7-1.638 1.533-1.638a1.48 1.48 0 0 1 1.082.466 1.52 1.52 0 0 1 .415 ********* 0 0 1-.033.242v.024H5.502c.033.257.156.494.346.668s.434.275.69.284a1.22 1.22 0 0 0 .958-.502l.022-.034.335.336-.016.022a1.672 1.672 0 0 1-1.3.664m.96-1.906a1.02 1.02 0 0 0-.326-.64 1 1 0 0 0-.661-.262 1.05 1.05 0 0 0-.667.266c-.185.165-.307.39-.345.636zM8.9 7.422V4.256h.511v.358a1.3 1.3 0 0 1 .987-.422h.162v.518h-.227a.9.9 0 0 0-.665.284.93.93 0 0 0-.257.684v1.744zM12.48 8.776a1.6 1.6 0 0 1-1.3-.66l-.016-.022.333-.338.024.032a1.25 1.25 0 0 0 .959.502 1.03 1.03 0 0 0 .734-.33 1.06 1.06 0 0 0 .284-.76v-.296a1.29 1.29 0 0 1-.987.452 1.5 1.5 0 0 1-1.053-.485 1.55 1.55 0 0 1-.41-1.097c-.01-.405.139-.797.413-1.091a1.5 1.5 0 0 1 1.05-.483 1.27 1.27 0 0 1 .987.452v-.396h.509V7.2a1.57 1.57 0 0 1-.438 1.113 1.53 1.53 0 0 1-1.09.463m.031-4.098c-.517 0-.953.502-.953 1.096s.436 1.096.953 1.096c.274-.013.532-.136.716-.342a1.06 1.06 0 0 0 .27-.754 1.06 1.06 0 0 0-.27-.754 1.03 1.03 0 0 0-.716-.342M16.41 7.486c-.739 0-1.275-.584-1.275-1.388V4.256h.51v1.842a.86.86 0 0 0 .224.63.84.84 0 0 0 .603.272c.51 0 .888-.43.888-1V4.256h.511v3.166h-.51v-.358a1.27 1.27 0 0 1-.952.422M21.26 7.422V5.548c0-.52-.32-.87-.789-.87a.92.92 0 0 0-.674.298.94.94 0 0 0-.247.702v1.744h-.517V4.256h.511v.388A1.25 1.25 0 0 1 20.5 4.2c.762 0 1.274.546 1.274 1.356V7.43zM25.128 7.422V5.548c0-.52-.32-.87-.79-.87a.92.92 0 0 0-.675.297.94.94 0 0 0-.248.703v1.744h-.51V4.256h.51v.388a1.25 1.25 0 0 1 .95-.444c.763 0 1.274.546 1.274 1.356V7.43zM26.764 7.422V4.256h.511v3.166zm.263-3.842a.32.32 0 0 1-.296-.2.33.33 0 0 1 .07-.353.318.318 0 0 1 .545.229.33.33 0 0 1-.197.3.3.3 0 0 1-.122.024M30.63 7.422V5.548c0-.52-.32-.87-.79-.87a.91.91 0 0 0-.674.297.94.94 0 0 0-.247.703v1.744h-.51V4.256h.51v.388a1.24 1.24 0 0 1 .947-.444c.764 0 1.274.546 1.274 1.356V7.43zM33.478 8.776a1.6 1.6 0 0 1-1.302-.66l-.014-.022.333-.338.022.032a1.25 1.25 0 0 0 .96.502 1.03 1.03 0 0 0 .735-.33 1.06 1.06 0 0 0 .284-.76v-.296a1.29 1.29 0 0 1-.987.452 1.5 1.5 0 0 1-1.055-.484 1.55 1.55 0 0 1-.41-1.098 1.55 1.55 0 0 1 .413-1.093A1.5 1.5 0 0 1 33.51 4.2a1.27 1.27 0 0 1 .987.452v-.396h.51V7.2a1.57 1.57 0 0 1-.438 1.113 1.53 1.53 0 0 1-1.09.463m.031-4.098c-.517 0-.954.502-.954 1.096s.438 1.096.954 1.096c.274-.013.532-.136.717-.342a1.06 1.06 0 0 0 .27-.754 1.06 1.06 0 0 0-.27-.754 1.03 1.03 0 0 0-.717-.342M38.393 7.422V5.548c0-.52-.32-.87-.789-.87a.91.91 0 0 0-.674.298.94.94 0 0 0-.247.702v1.744h-.511V2.836h.51v1.8a1.25 1.25 0 0 1 .95-.436c.761 0 1.274.546 1.274 1.356V7.43zM41.401 7.486a1.6 1.6 0 0 1-.912-.261 1.64 1.64 0 0 1-.613-.732 1.67 1.67 0 0 1 .33-1.808 1.605 1.605 0 0 1 1.776-.373c.299.124.555.335.735.607s.276.592.276.919c.004.432-.161.849-.46 1.157a1.6 1.6 0 0 1-1.132.491m0-2.8a1.1 1.1 0 0 0-.777.353 1.14 1.14 0 0 0-.304.807c0 .292.114.572.318.778a1.08 1.08 0 0 0 1.535 0c.203-.206.317-.486.317-.778a1.14 1.14 0 0 0-.304-.814 1.11 1.11 0 0 0-.785-.354zM45.177 7.486c-.738 0-1.275-.584-1.275-1.388V4.256h.511v1.842a.86.86 0 0 0 .222.628.84.84 0 0 0 .599.274c.509 0 .892-.43.892-1V4.256h.51v3.166h-.51v-.358a1.26 1.26 0 0 1-.95.422M49.07 7.486c-.813 0-1.498-.754-1.498-1.648S48.257 4.2 49.07 4.2a1.42 1.42 0 0 1 1.05.49V2.836h.51v4.586h-.51V7a1.44 1.44 0 0 1-1.05.486m0-2.8c-.466 0-.987.476-.987 1.16s.521 1.162.987 1.162c.548 0 1.05-.554 1.05-1.162s-.502-1.168-1.05-1.168zM53.125 7.486a1.59 1.59 0 0 1-1.116-.497 1.63 1.63 0 0 1-.444-1.151c0-.892.7-1.646 1.529-1.646a1.48 1.48 0 0 1 1.082.466 1.52 1.52 0 0 1 .415 ********* 0 0 1-.034.242v.024h-2.47a1.1 1.1 0 0 0 .345.68c.192.178.441.28.701.286a1.22 1.22 0 0 0 .96-.502l.023-.034.333.33-.016.022a1.667 1.667 0 0 1-1.308.664m.951-1.906a1.02 1.02 0 0 0-.325-.64 1 1 0 0 0-.661-.262c-.246.008-.48.103-.664.267s-.306.39-.344.635zM55.488 7.422V4.256H56v.358a1.3 1.3 0 0 1 .987-.422h.16v.518h-.226a.9.9 0 0 0-.664.284.93.93 0 0 0-.257.684v1.744zM2.015 11.748 3.767 9.78H4.88l-1.655 1.872L5 14.298H3.893L2.59 12.362 2.015 13v1.29h-.954V9.78h.954zM7.113 13.976a1.2 1.2 0 0 1-.86.386c-.638 0-1.052-.418-1.052-.904 0-.516.351-.876.923-.98l.987-.18c0-.26-.197-.452-.543-.452a.86.86 0 0 0-.7.322l-.477-.484a1.57 1.57 0 0 1 1.242-.614c.79 0 1.336.55 1.336 1.26v1.968h-.856zm0-1.034v-.096l-.7.128c-.223.04-.352.162-.352.356s.129.322.415.322c.382 0 .637-.252.637-.71M9.788 11.522a1.16 1.16 0 0 1 .89-.452c.7 0 1.21.518 1.21 1.388v1.84h-.86v-1.742a.6.6 0 0 0-.16-.46.6.6 0 0 0-.445-.186c-.38 0-.635.258-.635.742v1.646h-.86v-3.162h.86zM14.563 12.072a.7.7 0 0 0-.51-.258c-.256 0-.382.128-.382.258 0 .452 1.432.226 1.432 1.29 0 .55-.446 1-1.24 1a1.52 1.52 0 0 1-1.275-.58l.477-.484a.9.9 0 0 0 .67.354c.285 0 .413-.128.413-.29 0-.484-1.432-.246-1.432-1.29 0-.484.51-1 1.21-1a1.37 1.37 0 0 1 1.082.484zM17.588 12.072a.7.7 0 0 0-.509-.258c-.254 0-.383.128-.383.258 0 .452 1.435.226 1.435 1.29 0 .55-.446 1-1.243 1a1.52 1.52 0 0 1-1.273-.58l.478-.484a.9.9 0 0 0 .676.354c.286 0 .413-.128.413-.29 0-.484-1.433-.246-1.433-1.29 0-.484.51-1 1.212-1a1.35 1.35 0 0 1 1.08.484zM19.753 13.912v1.742h-.86v-4.518h.86v.386a1.19 1.19 0 0 1 .923-.452c.79 0 1.495.71 1.495 1.646s-.7 1.646-1.495 1.646a1.17 1.17 0 0 1-.923-.45m0-1.2a.79.79 0 0 0 .53.657.76.76 0 0 0 .808-.223.785.785 0 0 0-.156-1.175.76.76 0 0 0-.419-.125.78.78 0 0 0-.567.268.8.8 0 0 0-.196.602zM23.639 13c.042.186.148.35.299.464a.78.78 0 0 0 .525.156 1.07 1.07 0 0 0 .827-.386l.446.58a1.86 1.86 0 0 1-1.336.548 1.6 1.6 0 0 1-1.153-.47 1.65 1.65 0 0 1-.487-1.159 1.67 1.67 0 0 1 .463-1.169 1.62 1.62 0 0 1 1.144-.494 1.5 1.5 0 0 1 1.115.452 1.55 1.55 0 0 1 .445 1.13 1.7 1.7 0 0 1-.031.356zm0-.614h1.432a.7.7 0 0 0-.244-.422.7.7 0 0 0-.456-.158.75.75 0 0 0-.468.168.77.77 0 0 0-.264.426zM27.587 14.298h-.86V9.78h.86zM30.294 13.976a1.2 1.2 0 0 1-.86.386c-.637 0-1.051-.418-1.051-.904 0-.516.351-.876.923-.98l.986-.18c0-.26-.197-.452-.542-.452a.86.86 0 0 0-.7.322l-.478-.484a1.57 1.57 0 0 1 1.243-.614c.79 0 1.336.55 1.336 1.26v1.968h-.858zm0-1.034v-.096l-.7.128c-.223.04-.351.162-.351.356s.128.322.414.322c.383 0 .637-.252.637-.71M34.21 13.976a1.22 1.22 0 0 1-.891.386c-.7 0-1.21-.516-1.21-1.336v-1.89h.86v1.8a.61.61 0 0 0 .37.57q.113.046.234.044c.383 0 .637-.258.637-.742v-1.672h.86v3.162h-.86zM36.44 10.2h.763v1h.638v.774h-.638v1.26a.37.37 0 0 0 .118.258.36.36 0 0 0 .265.096.8.8 0 0 0 .318-.064v.742a1.8 1.8 0 0 1-.572.096c-.638 0-.987-.354-.987-1.096v-1.292h-.542V11.2h.288a.37.37 0 0 0 .264-.134.38.38 0 0 0 .085-.286zM40.102 14.362a1.6 1.6 0 0 1-.893-.293 1.64 1.64 0 0 1-.585-.745 1.67 1.67 0 0 1 .372-1.783 1.603 1.603 0 0 1 1.765-.343c.296.126.547.337.724.607s.272.587.272.911a1.7 1.7 0 0 1-.502 1.16c-.309.306-.722.48-1.153.486m0-.8a.82.82 0 0 0 .478-.108.853.853 0 0 0 .2-1.302.83.83 0 0 0-.913-.214.83.83 0 0 0-.384.31.85.85 0 0 0-.145.474.8.8 0 0 0 .21.579.8.8 0 0 0 .554.255zM44.4 11.974a1 1 0 0 0-.286-.032c-.446 0-.732.29-.732.84v1.516h-.86v-3.162h.86v.386a1.19 1.19 0 0 1 .891-.418h.127zM45.452 10.716a.47.47 0 0 1-.338-.142.487.487 0 0 1 0-.684.474.474 0 0 1 .676 0 .487.487 0 0 1 0 .684.47.47 0 0 1-.338.142m.446 3.582h-.858v-3.162h.858zM47.266 10.2h.766v1h.635v.774h-.636v1.26a.37.37 0 0 0 .117.258.36.36 0 0 0 .264.096q.167.002.32-.064v.742a1.8 1.8 0 0 1-.574.096c-.637 0-.987-.354-.987-1.096v-1.292h-.54V11.2h.286a.37.37 0 0 0 .264-.133.38.38 0 0 0 .085-.287zM50.164 13a.8.8 0 0 0 .303.461.78.78 0 0 0 .525.151 1.07 1.07 0 0 0 .827-.386l.446.58a1.86 1.86 0 0 1-1.336.548 1.6 1.6 0 0 1-1.224-.427 1.65 1.65 0 0 1-.527-1.198 1.67 1.67 0 0 1 .504-1.208 1.62 1.62 0 0 1 1.216-.451 1.5 1.5 0 0 1 1.114.452 1.55 1.55 0 0 1 .446 1.13q.004.18-.032.356zm0-.614h1.432a.7.7 0 0 0-.244-.422.7.7 0 0 0-.456-.158.75.75 0 0 0-.468.168.77.77 0 0 0-.264.426zM53.66 10.716a.47.47 0 0 1-.338-.142.487.487 0 0 1 .337-.826c.127 0 .248.051.338.142a.487.487 0 0 1 0 .684.47.47 0 0 1-.338.142m.445 3.582h-.86v-3.162h.86zM55.483 10.2h.763v1h.638v.774h-.638v1.26a.37.37 0 0 0 .118.258.36.36 0 0 0 .265.096.8.8 0 0 0 .318-.064v.742a1.8 1.8 0 0 1-.572.096c-.638 0-.987-.354-.987-1.096v-1.292h-.544V11.2h.286a.37.37 0 0 0 .265-.133.38.38 0 0 0 .086-.287zM58.018 17.024h-58v.968h58zM58.018.002h-58V.97h58z">
                            </path>
                        </g>
                        <defs>
                            <clipPath id="ksa-logo_svg__a">
                                <path fill="#fff" d="M0 0h58v18H0z"></path>
                            </clipPath>
                        </defs>
                    </svg>
                </a>
            </div>
            <div id="current-time" class="current-time-wrapper text-white"></div>
        </div>

        <div class="top-header">
            <div class="top-header-wrapper desktop-only">
                <div class="top-header-left">
                    <!--					--><?php //dynamic_sidebar( 'top-header-left' ); ?>
                    <?php include 'menu-parts/top-header-left.php'; ?>
                </div>
                <div class="top-header-right">
                    <div class="top-header-dynamic-sidebar">
                        <!--						--><?php //dynamic_sidebar( 'top-header-right' ); ?>
                        <?php include 'menu-parts/top-header-right.php'; ?>
                        <?php
                            switch ( $language ) {
                                case "EN":
                                    $language = "GB";
                                    break;
                                case "SV":
                                    $language = "SE";
                                    break;
                                case "EN-CA":
                                    $language = "CA";
                                    break;
                                case "NL-BE":
                                case "FR-BE":
                                    $language = "BE";
                                    break;
                                case "DA":
                                    $language = "DK";
                                    break;
                                case "NO":
                                    $language = "International";
                            }
                            ?>
                    </div>

                </div>
            </div>
            <div class="top-header-wrapper mobile-only">
                <?php
                        include 'menu-parts/menu-primary-left-small-devices.php';
                    ?>
            </div>

        </div>

        <?php
			//</editor-fold>
			if ( $show_lang_selector ) {
				include 'menu-parts/header-desktop-languages.php';
			}
			?>
        <nav class="navbar<?php echo ( in_array( 'fixed-header', get_body_class() ) ) ? ' fixed' : ''; ?>">

            <div class="navbar-wrapper clearfix">
                <div class="navbar-header">

                    <div class="navbar-brand">
                        <div class="burger-menu">
                            <a href="#openMainMenu" class="mobile-navbar-toggle ">
                                <i class="icon-wrapper menu">
                                    <svg width="24px" height="24px" fill="#FFFFFF" xmlns="http://www.w3.org/2000/svg"
                                        data-dn="Icon" viewBox="0 0 32 32" data-test-name="left-mobile-menu"
                                        type="hamburger" class="css-7mg0d1 ewhsrxj0">
                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                            d="M0 16c0-.71.672-1.286 1.5-1.286h17c.828 0 1.5.576 1.5 1.286 0 .71-.672 1.286-1.5 1.286h-17C.672 17.286 0 16.71 0 16ZM23 16c0-.71.672-1.286 1.5-1.286h6c.828 0 1.5.576 1.5 1.286 0 .71-.672 1.286-1.5 1.286h-6c-.828 0-1.5-.576-1.5-1.286ZM0 5.286C0 4.576.672 4 1.5 4h29c.828 0 1.5.576 1.5 1.286 0 .71-.672 1.285-1.5 1.285h-29c-.828 0-1.5-.575-1.5-1.285ZM0 26.714c0-.71.672-1.285 1.5-1.285h29c.828 0 1.5.575 1.5 1.285S31.328 28 30.5 28h-29C.672 28 0 27.424 0 26.714Z"
                                            fill="#FFFFFF"></path>
                                    </svg>
                                </i>
                            </a>
                        </div>
                        <?php
							if ( has_custom_logo() ) :
								//Hooks for rendering the logo. We didn't use the Wordpress general function as there are no hook to change the url.
								echo Niu_General_Hooks::render_custom_logo();
								echo Niu_General_Hooks::render_custom_logo( "mobile" );
							else : ?>
                        <h1>
                            <a href="<?php echo esc_url( home_url( '/' ) ); ?>"
                                title="<?php echo esc_attr( get_bloginfo( 'name' ) ); ?>">
                                <?php bloginfo( 'name' ); ?>
                            </a>
                        </h1>
                        <?php endif; ?>

                        <div id="main-menu">
                            <?php
                                include 'menu-parts/menu-primary-left.php';


                                //                            include 'menu-parts/menu-primary-right.php'; ?>
                        </div>

                    </div>


                    <?php

						$buttons = get_field('right_side_buttons', 'option');

						if(!empty($buttons) && is_array($buttons)) {

							echo '<ul id="menu-login-menu" class="login-menu">';
							foreach ( $buttons as $button ) { ?>
                    <li id="menu-item-39" class="right-side-btn menu-item menu-item-type-custom
										menu-item-object-custom menu-item-39">
                        <a href="<?= $button['button_link']; ?>">
                            <span class="menu-item-label"><?= $button['button_label']; ?></span>
                        </a>
                    </li>
                    <?php
							}
							echo '</ul>';
						}
						?>

                </div>
            </div>
            <!--.navbar-wrapper-->
        </nav>
    </div>
    <div class="main-menu-separator"></div>
    <div class="mobile-menu-wrappers">
        <div id="main-menu-mobile">

            <div class="button-close">
                <div class="btn-inner-wrapper ">
                    <img alt="" width="32px" height="32px"
                        src="https://www.unibet.nl/kwp-bundle-store/global-navigation/0.139.0/assets/icons/unibet_mobile.svg">
                    <span class="close-cross">
                        <i class="icon-wrapper icon-prefix sportsbook">
                            <svg width="16" height="16" fill="#FFFFFF" xmlns="http://www.w3.org/2000/svg" data-dn="Icon"
                                viewBox="0 0 32 32" data-test-name="close-main-menu" type="cross"
                                class="css-7mg0d1 ewhsrxj0">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M31.56 31.56a1.5 1.5 0 0 1-2.12 0l-29-29A1.5 1.5 0 1 1 2.56.44l29 29a1.5 1.5 0 0 1 0 2.12Z"
                                    fill="#FFFFFF"></path>
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M.44 31.56a1.5 1.5 0 0 1 0-2.12l14.5-14.5a1.5 1.5 0 0 1 2.12 2.12l-14.5 14.5a1.5 1.5 0 0 1-2.12 0ZM19.182 12.819a1.5 1.5 0 0 1 0-2.122L29.428.44a1.5 1.5 0 1 1 2.123 2.12L21.304 12.818a1.5 1.5 0 0 1-2.122 0Z"
                                    fill="#FFFFFF"></path>
                            </svg>
                        </i>
                    </span>
                </div>
            </div>
            <?php
					if ( has_nav_menu( 'primary' ) ) {
						wp_nav_menu( [
							'theme_location' => 'primary',
							'menu_class'     => 'nav navbar-nav',
							'container'      => '',
							'walker'         => new Niu_Mobile_Walker()
						] );
					}
					?>

            <div class="dynamic-header-sidebar">
                <div class="menu-top-header-right-menu-container">
                    <ul id="menu-top-header-right-menu-1" class="menu">
                        <?php
                                if ( has_nav_menu( 'top-right' ) ) {
                                    wp_nav_menu( [
                                        'theme_location' => 'top-right',
                                        'menu_class'     => 'menu',
                                        'container'      => '',
                                        'walker'         => new Niu_SVG_Icon_Walker()
                                    ] );
                                }
                                ?>

                    </ul>
                </div>
            </div>
        </div>
        <div id="main-sub-menu-mobile">
            <div class="button-close">
                <div class="btn-inner-wrapper">
                    <div class="title-spacer">
                        <span class="dropdown-toggle vct-icon-dropdown back-btn">
                            <i class="icon-wrapper icon-prefix">
                                <svg width="16px" height="16px" fill="#FFFFFF" xmlns="http://www.w3.org/2000/svg"
                                    data-dn="Icon" viewBox="0 0 32 32" data-test-name="main-menu-back-icon"
                                    type="arrowLeft" class="css-7mg0d1 ewhsrxj0">
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                        d="M25.67.56c.517.644.416 1.588-.226 2.108L8.984 16l16.46 13.332c.642.52.743 1.464.226 2.109a1.486 1.486 0 0 1-2.098.227L7.112 18.336a3.01 3.01 0 0 1 0-4.672L23.572.332a1.486 1.486 0 0 1 2.098.227Z"
                                        fill="#FFFFFF"></path>
                                </svg>
                            </i>
                        </span>
                    </div>
                    <div class="title"></div>
                    <span class="close-cross">
                        <i class="icon-wrapper icon-prefix sportsbook">
                            <svg width="16" height="16" fill="#FFFFFF" xmlns="http://www.w3.org/2000/svg" data-dn="Icon"
                                viewBox="0 0 32 32" data-test-name="close-main-menu" type="cross"
                                class="css-7mg0d1 ewhsrxj0">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M31.56 31.56a1.5 1.5 0 0 1-2.12 0l-29-29A1.5 1.5 0 1 1 2.56.44l29 29a1.5 1.5 0 0 1 0 2.12Z"
                                    fill="#FFFFFF"></path>
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M.44 31.56a1.5 1.5 0 0 1 0-2.12l14.5-14.5a1.5 1.5 0 0 1 2.12 2.12l-14.5 14.5a1.5 1.5 0 0 1-2.12 0ZM19.182 12.819a1.5 1.5 0 0 1 0-2.122L29.428.44a1.5 1.5 0 1 1 2.123 2.12L21.304 12.818a1.5 1.5 0 0 1-2.122 0Z"
                                    fill="#FFFFFF"></path>
                            </svg>
                        </i>
                    </span>
                </div>
            </div>
            <ul id="menu-primary-menu-left" class="nav navbar-nav">
                <?php //populated by js ?>
            </ul>

        </div>

    </div>
    <!--#main-menu-->
    <div class="header-image">
    </div>
</header>
<?php endif;
?>
<?php //</editor-fold> ?>

<?php
//<editor-fold desc="Original Menu">
if(false){
?>
<header id="site-header" class="site-header dynamic-header <?php echo esc_attr( hello_get_header_layout_class() ); ?>"
    role="banner">
    <?php if ( is_active_sidebar( 'header-top-bar' ) ) : ?>
    <div class="header-top-bar">
        <div class="header-inner">
            <div class="top-left">
                <div class="today-date">
                    <i class='far fa-calendar-alt'></i>
                    <span><?php echo date( 'l, F d, Y' ); ?></span>
                </div>
                <div class="today-whether">
                    <?php echo do_shortcode('[location-weather id="13935"]') ?>
                </div>
                <div class="breaking-news">
                    <strong><i class="fas fa-bolt"></i>Breaking News :</strong>
                    <?php
							$postArgs = [
								'post_type' 	=> 'post',
								'post_status' 	=> 'publish',
								'orderby' 		=> 'date',
								'order' 		=> 'DESC',
								'posts_per_page'=> 5
							];
							$postLoop = get_posts( $postArgs );
							if( !empty( $postLoop ) ) {
								echo '<ul>';
								foreach( $postLoop as $post_loop ) {
									echo '<li><a href="'.get_the_permalink( $post_loop->ID ).'">'.$post_loop->post_title.'</a></li>';
								}
								echo '</ul>';
							}
						?>
                </div>
            </div>
            <?php dynamic_sidebar( 'header-top-bar' ); ?>
        </div>
    </div>
    <?php endif; ?>

    <div class="header-inner">
        <div class="site-branding show-<?php echo hello_elementor_get_setting( 'hello_header_logo_type' ); ?>">
            <?php if ( has_custom_logo() && ( 'title' !== hello_elementor_get_setting( 'hello_header_logo_type' ) || $is_editor ) ) : ?>
            <div class="site-logo <?php echo hello_show_or_hide( 'hello_header_logo_display' ); ?>">
                <?php the_custom_logo(); ?>
            </div>
            <?php endif;

			if ( $site_name && ( 'logo' !== hello_elementor_get_setting( 'hello_header_logo_type' ) || $is_editor ) ) : ?>
            <h1 class="site-title <?php echo hello_show_or_hide( 'hello_header_logo_display' ); ?>">
                <a href="<?php echo esc_url( home_url( '/' ) ); ?>"
                    title="<?php esc_attr_e( 'Home', 'hello-elementor' ); ?>" rel="home">
                    <?php echo esc_html( $site_name ); ?>
                </a>
            </h1>
            <?php endif;

			if ( $tagline && ( hello_elementor_get_setting( 'hello_header_tagline_display' ) || $is_editor ) ) : ?>
            <p class="site-description <?php echo hello_show_or_hide( 'hello_header_tagline_display' ); ?> ">
                <?php echo esc_html( $tagline ); ?>
            </p>
            <?php endif; ?>
        </div>

        <?php if ( $header_nav_menu ) : ?>
        <nav class="site-navigation <?php echo hello_show_or_hide( 'hello_header_menu_display' ); ?>" role="navigation">
            <?php echo $header_nav_menu; ?>
        </nav>
        <div class="site-navigation-toggle-holder <?php echo hello_show_or_hide( 'hello_header_menu_display' ); ?>">
            <div class="site-navigation-toggle">
                <i class="icon-menu"></i>
                <span class="elementor-screen-only">Menu</span>
            </div>
        </div>
        <nav class="site-navigation-dropdown <?php echo hello_show_or_hide( 'hello_header_menu_display' ); ?>"
            role="navigation">
            <a href="javascript:;" class="close-menu">close <i class="icon-close"></i></a>
            <div class="menu-header">
                <div class="top-menu">
                    <div class="today-date">
                        <i class='far fa-calendar-alt'></i>
                        <span><?php echo date( 'l, F d, Y' ); ?></span>
                    </div>
                    <div class="today-whether">
                        <i class='fas fa-cloud-sun'></i>
                        <?php echo do_shortcode('[location-weather id="13935"]') ?>
                    </div>
                </div>
                <div class="breaking-news">
                    <strong><i class="fas fa-bolt"></i>Breaking News :</strong>
                    <?php
							$postArgs = [
								'post_type' 	=> 'post',
								'post_status' 	=> 'publish',
								'orderby' 		=> 'date',
								'order' 		=> 'DESC',
								'posts_per_page'=> 5
							];
							$postLoop = get_posts( $postArgs );
							if( !empty( $postLoop ) ) {
								echo '<ul>';
								foreach( $postLoop as $post_loop ) {
									echo '<li><a href="'.get_the_permalink( $post_loop->ID ).'">'.$post_loop->post_title.'</a></li>';
								}
								echo '</ul>';
							}
						?>
                </div>
            </div>


            <?php if ( has_custom_logo() && ( 'title' !== hello_elementor_get_setting( 'hello_header_logo_type' ) || $is_editor ) ) : ?>
            <div class="site-logo <?php echo hello_show_or_hide( 'hello_header_logo_display' ); ?>">
                <?php the_custom_logo(); ?>
            </div>
            <?php endif; ?>

            <?php echo $header_nav_menu; ?>

            <div class="menu-footer">
                <?php if ( is_active_sidebar( 'header-top-bar' ) ) : ?>
                <?php dynamic_sidebar( 'header-top-bar' ); ?>
                <?php endif; ?>
                <p class="copyright">
                    <?php	printf( esc_html__( '&copy; %1$s, %2$s All rights reserved.', 'hello-elementor' ), esc_html( date( 'Y' ) ),  __('<strong> Unibet.</strong>' ) ); ?>
                </p>
            </div>
        </nav>
        <?php endif; ?>
        <div class="header-search-area">
            <?php echo do_shortcode('[wd_asp id=1]'); ?>
            <?php /*<form role="search" method="get" class="search-form" action="<?php echo site_url(); ?>">
            <button type="submit" class="search-submit"><i class="icon-search"></i></button>
            <input type="hidden" name="post_type" value="post">
            <label>
                <input type="text" class="search-field" placeholder="Search..."
                    value="<?php echo ( ( isset( $_GET['s'] ) ) ? $_GET['s'] : '' ); ?>" name="s">
            </label>
            </form> */?>
        </div>
    </div>
</header>
<?php }
//</editor-fold>
?>