.elementor-kit-11516{--e-global-color-primary:#6EC1E4;--e-global-color-secondary:#54595F;--e-global-color-text:#7A7A7A;--e-global-color-accent:#61CE70;--e-global-typography-primary-font-family:"Roboto";--e-global-typography-primary-font-weight:600;--e-global-typography-secondary-font-family:"Roboto Slab";--e-global-typography-secondary-font-weight:400;--e-global-typography-text-font-family:"Roboto";--e-global-typography-text-font-weight:400;--e-global-typography-accent-font-family:"Roboto";--e-global-typography-accent-font-weight:500;background-color:#F3F3F3;color:#00000099;font-family:"Roboto", Sans-serif;font-size:16px;line-height:22px;}.elementor-kit-11516 e-page-transition{background-color:#FFBC7D;}.elementor-section.elementor-section-boxed > .elementor-container{max-width:1140px;}.e-con{--container-max-width:1140px;}.elementor-widget:not(:last-child){margin-block-end:20px;}.elementor-element{--widgets-spacing:20px 20px;--widgets-spacing-row:20px;--widgets-spacing-column:20px;}{}h1.entry-title{display:var(--page-title-display);}.site-header{padding-inline-end:0px;padding-inline-start:0px;}.site-header .site-navigation ul.menu li a{color:#FFFFFF;}.site-header .site-navigation-toggle .site-navigation-toggle-icon{color:#000000;}@media(max-width:1024px){.elementor-section.elementor-section-boxed > .elementor-container{max-width:1024px;}.e-con{--container-max-width:1024px;}}@media(max-width:767px){.elementor-kit-11516{font-size:14px;}.elementor-section.elementor-section-boxed > .elementor-container{max-width:767px;}.e-con{--container-max-width:767px;}}/* Start custom CSS */.breadcrumb a[href*="casinos"], .breadcrumb span:first-of-type {
    display: none;
}

.game-visit-box 
{
    visibility: hidden; 
}

footer {
    display: none;
}

.elementor-section.elementor-section-stretched footer {
    display: block;
}


.button {
    visibility: hidden;  /* Hides the original text */
    position: relative;   /* Necessary for positioning the pseudo-element */
    display: inline-flex; /* Use inline-flex to make sure it behaves like a button */
    justify-content: center;  /* Horizontally center the content */
    align-items: center;  /* Vertically center the content */
    padding: 0;  /* Ensure no extra padding is added */
    border: none;  /* Remove any default borders */
    height: auto;  /* Adjust height according to content */
    width: auto;   /* Ensure the button size adjusts to its content */
}

.button::after {
    content: "Lees Meer";  /* The new text */
    visibility: visible;
    position: absolute;  /* Absolutely positioned relative to .button */
    background: #ffe71f;
    border: 1px solid #ffe71f;
    color: #000;
    display: inline-block;
    cursor: pointer;
    font-weight: 800;
    font-size: 14px;
    line-height: 16px;
    padding: 14px 24px 12px;
    text-transform: uppercase;
    text-align: center;  /* Make sure text is centered within the pseudo-element */
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%); /* Centers the pseudo-element inside the parent */
    white-space: nowrap; /* Prevents text from wrapping */
}/* End custom CSS */