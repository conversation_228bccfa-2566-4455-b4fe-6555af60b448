<?php exit('Access denied'); __halt_compiler(); ?>
******************************************************************
This file is used by the Wordfence Web Application Firewall. Read 
more at https://docs.wordfence.com/en/Web_Application_Firewall_FAQ
******************************************************************
a:25:{s:6:"apiKey";s:160:"7a9afcf428a863bf780bc6e137b2a348f9833c549cba9f6bb4398a75213ff76425655414e86c3dcb87c5e37473036789d06d612eb35f688b93c0049f70470a831cd255dea4a31b046f2af3c08133a3b0";s:6:"isPaid";b:0;s:7:"siteURL";s:27:"http://sitebuilder-ms.local";s:7:"homeURL";s:27:"http://sitebuilder-ms.local";s:14:"whitelistedIPs";s:53:"*************,*************,***********,*************";s:9:"howGetIPs";s:0:"";s:25:"howGetIPs_trusted_proxies";N;s:33:"howGetIPs_trusted_proxies_unified";s:0:"";s:13:"pluginABSPATH";s:49:"/Users/<USER>/Local Sites/sitebuilder/app/public/";s:11:"other_WFNet";b:1;s:9:"serverIPs";s:30:"["**************","127.0.0.1"]";s:15:"blockCustomText";s:0:"";s:13:"timeoffset_wf";s:1:"0";s:23:"advancedBlockingEnabled";s:1:"1";s:20:"disableWAFIPBlocking";s:1:"0";s:13:"patternBlocks";s:2:"[]";s:13:"countryBlocks";s:154:"{"blocks":[],"action":"block","loggedInBlocked":"0","bypassRedirURL":"","bypassRedirDest":"","bypassViewURL":"","redirURL":"","cookieVal":"66a0b73f4b211"}";s:11:"otherBlocks";s:34:"{"blockedTime":"7200","blocks":[]}";s:8:"lockouts";s:36:"{"lockedOutTime":3600,"lockouts":[]}";s:21:"whitelistedServiceIPs";s:10121:"{"private":["10.0.0.0\/8","*********\/8","**********\/12","*********\/29","***********\/16"],"wordfence":["************","**********\/27","2605:2400:0104:0100::\/56"],"sucuri":["*************","**************","***************","************","*************","************","************","************","*************","*************","**************","*************","**************","**************","************","**************","*************","*************","***************","***************","**************","*************","**************","*************","**************"],"facebook":["***********\/20","************\/20","************\/21","***********\/21","***********\/21","***********\/22","************\/24","************\/18","************\/19","************\/20","**********\/22","***********\/24","************\/19","************\/24","**********\/18","**********\/21","************\/21","************\/24","************\/24","************\/20","**********\/19","**********\/24","31.13.65.0\/24","31.13.67.0\/24","31.13.68.0\/24","31.13.69.0\/24","31.13.70.0\/24","31.13.71.0\/24","31.13.72.0\/24","31.13.73.0\/24","31.13.74.0\/24","31.13.75.0\/24","31.13.76.0\/24","31.13.77.0\/24","31.13.96.0\/19","31.13.66.0\/24","173.252.96.0\/19","69.63.178.0\/24","31.13.78.0\/24","31.13.79.0\/24","31.13.80.0\/24","31.13.82.0\/24","31.13.83.0\/24","31.13.84.0\/24","31.13.85.0\/24","31.13.86.0\/24","31.13.87.0\/24","31.13.88.0\/24","31.13.89.0\/24","31.13.90.0\/24","31.13.91.0\/24","31.13.92.0\/24","31.13.93.0\/24","31.13.94.0\/24","31.13.95.0\/24","69.171.253.0\/24","69.63.186.0\/24","31.13.81.0\/24","179.60.192.0\/22","179.60.192.0\/24","179.60.193.0\/24","179.60.194.0\/24","179.60.195.0\/24","185.60.216.0\/22","45.64.40.0\/22","185.60.216.0\/24","185.60.217.0\/24","185.60.218.0\/24","185.60.219.0\/24","129.134.0.0\/16","157.240.0.0\/16","157.240.8.0\/24","157.240.0.0\/24","157.240.1.0\/24","157.240.2.0\/24","157.240.3.0\/24","157.240.4.0\/24","157.240.5.0\/24","157.240.6.0\/24","157.240.7.0\/24","157.240.9.0\/24","157.240.10.0\/24","157.240.16.0\/24","157.240.19.0\/24","157.240.11.0\/24","157.240.12.0\/24","157.240.13.0\/24","157.240.14.0\/24","157.240.15.0\/24","157.240.17.0\/24","157.240.18.0\/24","157.240.20.0\/24","157.240.21.0\/24","157.240.22.0\/24","157.240.23.0\/24","157.240.0.0\/17","69.171.250.0\/24","157.240.24.0\/24","157.240.25.0\/24","199.201.64.0\/24","199.201.65.0\/24","199.201.64.0\/22","204.15.20.0\/22","157.240.192.0\/24","129.134.0.0\/17","204.15.20.0\/22","***********\/20","***********\/21","***********\/21","************\/20","***********\/20","2620:0:1c00::\/40","2a03:2880::\/32","2a03:2880:fffe::\/48","2a03:2880:ffff::\/48","2620:0:1cff::\/48","2a03:2880:f000::\/48","2a03:2880:f001::\/48","2a03:2880:f002::\/48","2a03:2880:f003::\/48","2a03:2880:f004::\/48","2a03:2880:f005::\/48","2a03:2880:f006::\/48","2a03:2880:f007::\/48","2a03:2880:f008::\/48","2a03:2880:f009::\/48","2a03:2880:f00a::\/48","2a03:2880:f00b::\/48","2a03:2880:f00c::\/48","2a03:2880:f00d::\/48","2a03:2880:f00e::\/48","2a03:2880:f00f::\/48","2a03:2880:f010::\/48","2a03:2880:f011::\/48","2a03:2880:f012::\/48","2a03:2880:f013::\/48","2a03:2880:f014::\/48","2a03:2880:f015::\/48","2a03:2880:f016::\/48","2a03:2880:f017::\/48","2a03:2880:f018::\/48","2a03:2880:f019::\/48","2a03:2880:f01a::\/48","2a03:2880:f01b::\/48","2a03:2880:f01c::\/48","2a03:2880:f01d::\/48","2a03:2880:f01e::\/48","2a03:2880:f01f::\/48","2a03:2880:1000::\/36","2a03:2880:2000::\/36","2a03:2880:3000::\/36","2a03:2880:4000::\/36","2a03:2880:5000::\/36","2a03:2880:6000::\/36","2a03:2880:7000::\/36","2a03:2880:f020::\/48","2a03:2880:f021::\/48","2a03:2880:f022::\/48","2a03:2880:f023::\/48","2a03:2880:f024::\/48","2a03:2880:f025::\/48","2a03:2880:f026::\/48","2a03:2880:f027::\/48","2a03:2880:f028::\/48","2a03:2880:f029::\/48","2a03:2880:f02b::\/48","2a03:2880:f02c::\/48","2a03:2880:f02d::\/48","2a03:2880:f02e::\/48","2a03:2880:f02f::\/48","2a03:2880:f030::\/48","2a03:2880:f031::\/48","2a03:2880:f032::\/48","2a03:2880:f033::\/48","2a03:2880:f034::\/48","2a03:2880:f035::\/48","2a03:2880:f036::\/48","2a03:2880:f037::\/48","2a03:2880:f038::\/48","2a03:2880:f039::\/48","2a03:2880:f03a::\/48","2a03:2880:f03b::\/48","2a03:2880:f03c::\/48","2a03:2880:f03d::\/48","2a03:2880:f03e::\/48","2a03:2880:f03f::\/48","2401:db00::\/32","2a03:2880::\/36","2803:6080::\/32","2a03:2880:f100::\/48","2a03:2880:f200::\/48","2a03:2880:f101::\/48","2a03:2880:f201::\/48","2a03:2880:f102::\/48","2a03:2880:f202::\/48","2a03:2880:f103::\/48","2a03:2880:f203::\/48","2a03:2880:f104::\/48","2a03:2880:f204::\/48","2a03:2880:f107::\/48","2a03:2880:f207::\/48","2a03:2880:f108::\/48","2a03:2880:f208::\/48","2a03:2880:f109::\/48","2a03:2880:f209::\/48","2a03:2880:f10a::\/48","2a03:2880:f20a::\/48","2a03:2880:f10b::\/48","2a03:2880:f20b::\/48","2a03:2880:f10d::\/48","2a03:2880:f20d::\/48","2a03:2880:f10e::\/48","2a03:2880:f20e::\/48","2a03:2880:f10f::\/48","2a03:2880:f20f::\/48","2a03:2880:f110::\/48","2a03:2880:f210::\/48","2a03:2880:f111::\/48","2a03:2880:f211::\/48","2a03:2880:f112::\/48","2a03:2880:f212::\/48","2a03:2880:f114::\/48","2a03:2880:f214::\/48","2a03:2880:f115::\/48","2a03:2880:f215::\/48","2a03:2880:f116::\/48","2a03:2880:f216::\/48","2a03:2880:f117::\/48","2a03:2880:f217::\/48","2a03:2880:f118::\/48","2a03:2880:f218::\/48","2a03:2880:f119::\/48","2a03:2880:f219::\/48","2a03:2880:f11a::\/48","2a03:2880:f21a::\/48","2a03:2880:f11f::\/48","2a03:2880:f21f::\/48","2a03:2880:f121::\/48","2a03:2880:f221::\/48","2a03:2880:f122::\/48","2a03:2880:f222::\/48","2a03:2880:f123::\/48","2a03:2880:f223::\/48","2a03:2880:f10c::\/48","2a03:2880:f20c::\/48","2a03:2880:f126::\/48","2a03:2880:f226::\/48","2a03:2880:f105::\/48","2a03:2880:f205::\/48","2a03:2880:f125::\/48","2a03:2880:f225::\/48","2a03:2880:f106::\/48","2a03:2880:f206::\/48","2a03:2880:f11b::\/48","2a03:2880:f21b::\/48","2a03:2880:f113::\/48","2a03:2880:f213::\/48","2a03:2880:f11c::\/48","2a03:2880:f21c::\/48","2a03:2880:f128::\/48","2a03:2880:f228::\/48","2a03:2880:f02a::\/48","2a03:2880:f12a::\/48","2a03:2880:f22a::\/48","2a03:2880:f12f::\/48","2a03:2880:f22f::\/48","2a03:2880:f11d::\/48","2a03:2880:f11e::\/48","2a03:2880:f120::\/48","2a03:2880:f124::\/48","2a03:2880:f127::\/48","2a03:2880:f129::\/48","2a03:2880:f12b::\/48","2a03:2880:f12c::\/48","2a03:2880:f12d::\/48","2a03:2880:f12e::\/48","2a03:2880:f130::\/48","2a03:2880:f131::\/48","2a03:2880:f132::\/48","2a03:2880:f133::\/48","2a03:2880:f134::\/48","2a03:2880:f135::\/48","2a03:2880:f136::\/48","2a03:2880:f137::\/48","2a03:2880:f138::\/48","2a03:2880:f139::\/48","2a03:2880:f13a::\/48","2a03:2880:f13b::\/48","2a03:2880:f13c::\/48","2a03:2880:f13d::\/48","2a03:2880:f13e::\/48","2a03:2880:f13f::\/48","2a03:2880:f21d::\/48","2a03:2880:f21e::\/48","2a03:2880:f220::\/48","2a03:2880:f224::\/48","2a03:2880:f227::\/48","2a03:2880:f229::\/48","2a03:2880:f22b::\/48","2a03:2880:f22c::\/48","2a03:2880:f22d::\/48","2a03:2880:f22e::\/48","2a03:2880:f230::\/48","2a03:2880:f231::\/48","2a03:2880:f232::\/48","2a03:2880:f233::\/48","2a03:2880:f234::\/48","2a03:2880:f235::\/48","2a03:2880:f236::\/48","2a03:2880:f237::\/48","2a03:2880:f238::\/48","2a03:2880:f239::\/48","2a03:2880:f23a::\/48","2a03:2880:f23b::\/48","2a03:2880:f23c::\/48","2a03:2880:f23d::\/48","2a03:2880:f23e::\/48","2a03:2880:f23f::\/48","2a03:2880:f0ff::\/48","2a03:2880:f1ff::\/48","2a03:2880:f2ff::\/48","2c0f:ef78:0003::\/48"],"uptimerobot":["69.162.124.224\/28","63.143.42.240\/28"],"statuscake":["103.194.112.70","104.131.247.151","104.131.248.65","104.131.248.78","104.156.229.24","104.156.255.184","104.206.168.26","104.238.164.105","107.150.1.135","107.155.104.182","107.155.108.234","107.155.125.29","107.161.28.219","107.170.197.248","107.170.219.46","107.170.227.23","107.170.227.24","107.170.240.141","107.170.53.191","107.191.47.131","107.191.57.237","108.61.119.153","108.61.162.214","108.61.205.201","108.61.212.141","108.61.215.179","125.63.48.239","128.199.222.65","138.197.130.232","138.197.130.235","138.197.140.243","138.204.171.136","138.68.24.115","138.68.24.136","138.68.24.207","138.68.24.60","138.68.80.10","138.68.80.173","139.59.15.79","139.59.155.26","139.59.190.241","139.59.22.109","139.59.26.85","139.59.29.167","149.154.157.61","149.255.59.100","151.236.10.238","151.236.18.80","151.80.175.223","151.80.175.226","154.127.60.23","154.127.60.59","158.255.208.76","159.203.182.22","159.203.182.60","159.203.186.225","159.203.31.18","162.243.247.163","162.243.71.56","162.248.97.72","162.253.64.104","162.253.64.87","176.56.230.110","178.62.101.57","178.62.104.137","178.62.106.84","178.62.109.7","178.62.40.233","178.62.41.44","178.62.41.49","178.62.41.52","178.62.65.162","178.62.71.227","178.62.78.199","178.62.80.93","178.62.86.69","178.73.210.99","181.41.201.117","181.41.214.137","185.112.157.185","185.12.45.70","185.47.129.168","185.60.135.86","188.166.158.224","188.166.253.148","188.226.139.158","188.226.158.160","188.226.169.228","188.226.171.58","188.226.184.152","188.226.185.106","188.226.186.199","188.226.203.84","188.226.247.184","188.68.238.79","192.241.221.11","193.124.178.54","193.124.178.61","193.182.144.105","193.182.144.147","199.167.128.80","209.222.30.242","213.183.56.107","217.148.43.188","217.148.43.202","31.220.7.237","37.157.246.146","37.235.48.42","37.235.52.25","37.235.53.240","37.235.55.205","37.97.188.103","45.32.128.80","45.32.145.79","45.32.151.21","45.32.160.172","45.32.166.195","45.32.171.24","45.32.192.198","45.32.195.186","45.32.195.93","45.32.212.56","45.32.36.158","45.32.7.22","45.63.121.159","45.63.26.78","45.63.51.63","45.63.61.213","45.63.76.68","45.63.78.84","45.63.86.120","45.63.88.213","45.76.1.44","45.76.192.50","45.76.3.112","46.101.0.24","46.101.110.32","46.101.110.43","46.101.110.45","46.101.20.96","46.101.238.182","46.101.238.189","46.101.240.208","46.101.27.186","46.101.61.83","46.101.74.251","5.45.179.103","50.2.139.16","82.221.95.161","91.236.116.163"],"seznam":["77.75.74.0\/24","77.75.76.0\/24","77.75.77.0\/24","77.75.78.0\/24","77.75.79.0\/24","2a02:598:a::78:0\/112","2a02:598:a::79:0\/112","2a02:598:2::0\/96"]}";s:25:"detectProxyRecommendation";s:0:"";s:16:"wordpressVersion";s:5:"6.5.4";s:23:"wordpressPluginVersions";a:33:{s:22:"advanced-custom-fields";s:5:"6.3.1";s:26:"advanced-custom-fields-pro";s:7:"6.3.1.2";s:15:"ajax-search-pro";s:6:"4.26.2";s:14:"classic-editor";s:5:"1.6.3";s:13:"cookie-notice";s:5:"2.4.9";s:19:"custom-post-type-ui";s:6:"1.14.0";s:34:"jwt-authentication-for-wp-rest-api";s:5:"1.2.6";s:9:"elementor";s:6:"3.22.1";s:13:"elementor-pro";s:6:"3.22.0";s:12:"gravityforms";s:6:"2.7.17";s:7:"imagify";s:5:"2.2.2";s:16:"location-weather";s:5:"2.0.8";s:7:"members";s:5:"3.2.9";s:8:"meta-box";s:5:"5.9.8";s:14:"meta-box-group";s:6:"1.3.17";s:32:"ns-cloner-site-copier-cron-addon";s:0:"";s:21:"ns-cloner-site-copier";s:7:"4.2.2.2";s:10:"poll-maker";s:5:"5.2.8";s:16:"seo-by-rank-math";s:7:"1.0.220";s:15:"wp-rest-api-log";s:5:"1.7.0";s:8:"safe-svg";s:5:"2.1.1";s:12:"salient-core";s:5:"2.0.9";s:19:"js_composer_salient";s:3:"7.6";s:26:"seedprod-coming-soon-pro-5";s:9:"6.15.13.1";s:11:"simple-tags";s:6:"3.21.0";s:14:"visualcomposer";s:6:"45.9.0";s:9:"wordfence";s:6:"7.11.7";s:16:"wp-api-swaggerui";s:5:"1.1.2";s:11:"wp-crontrol";s:6:"1.16.3";s:26:"wp-date-and-time-shortcode";s:5:"2.6.5";s:14:"wps-hide-login";s:8:"1.9.16.6";s:13:"wordpress-seo";s:4:"22.8";s:22:"zzzzz-niu-custom-tools";s:3:"0.1";}s:22:"wordpressThemeVersions";a:21:{s:20:"TwentyTwentyOneChild";s:3:"1.0";s:20:"betting-news-branded";s:5:"1.0.0";s:22:"betting-news-unbranded";s:5:"1.0.0";s:19:"casino-news-branded";s:5:"1.0.0";s:21:"casino-news-unbranded";s:5:"1.0.0";s:23:"construction-firm-child";s:3:"1.0";s:17:"construction-firm";s:5:"0.2.7";s:21:"hello-elementor-child";s:5:"1.0.1";s:15:"hello-elementor";s:5:"3.0.2";s:13:"salient-child";s:3:"1.0";s:7:"salient";s:6:"16.2.2";s:22:"sports-tournament-dark";s:3:"1.0";s:23:"sports-tournament-light";s:3:"1.0";s:19:"super-minimal-child";s:3:"1.0";s:13:"super-minimal";s:5:"0.2.0";s:14:"twentynineteen";s:3:"2.0";s:12:"twentytwenty";s:3:"1.7";s:16:"twentytwentyfour";s:3:"1.1";s:15:"twentytwentyone";s:3:"1.8";s:17:"twentytwentythree";s:3:"1.2";s:15:"twentytwentytwo";s:3:"1.4";}s:6:"WPLANG";s:0:"";}