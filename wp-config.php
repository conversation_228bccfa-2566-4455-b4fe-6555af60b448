<?php
define( 'WP_CACHE', true ); // Added by WP Rocket

@ini_set( 'upload_max_size' , '128M' );
@ini_set( 'post_max_size', '128M');
@ini_set( 'max_execution_time', '300' );
@ini_set( 'max_input_time', '300' );

/**
 * The base configuration for WordPress
 *
 * The wp-config.php creation script uses this file during the installation.
 * You don't have to use the web site, you can copy this file to "wp-config.php"
 * and fill in the values.
 *
 * This file contains the following configurations:
 *
 * * Database settings
 * * Secret keys
 * * Database table prefix
 * * Localized language
 * * ABSPATH
 *
 * @link https://wordpress.org/support/article/editing-wp-config-php/
 *
 * @package WordPress
 */

// ** Database settings - You can get this info from your web host ** //
/** The name of the database for WordPress */
define( 'DB_NAME', $_SERVER['DB_NAME'] ?? 'sitebuilder');

/** Database username */
define( 'DB_USER', $_SERVER['DB_USER'] ?? 'root');

/** Database password */
define( 'DB_PASSWORD', $_SERVER['DB_PASSWORD'] ?? 'WebDev2025Q2');

/** Database hostname */
define( 'DB_HOST', $_SERVER['DB_HOST'] ?? '127.0.0.1:3306');

/** Database charset to use in creating database tables. */
define('DB_CHARSET', 'utf8');

/** The database collate type. Don't change this if in doubt. */
define('DB_COLLATE', '');

/**#@+
 * Authentication unique keys and salts.
 *
 * Change these to different unique phrases! You can generate these using
 * the {@link https://api.wordpress.org/secret-key/1.1/salt/ WordPress.org secret-key service}.
 *
 * You can change these at any point in time to invalidate all existing cookies.
 * This will force all users to have to log in again.
 *
 * @since 2.6.0
 */
define('AUTH_KEY',         '1uzbXQbDa_qOap;(BAO3VBg?tC)Lz(8>L,As4,7U6J#GXn{.jKxmYz`@uv{6u6Me');
define('SECURE_AUTH_KEY',  'EM@=jZ9(%l*-&lZW3N0W}-VNw8>-&>CcaL1tIyA%|EKa8{2rmC!7(TP*$f9%9^p$');
define('LOGGED_IN_KEY',    '=(qY]^yVpr|qCa{+xNmXEj<?F%Z+s<Ev-AJb(),o#}o?boLiOn;T]k}!Y_dmqb79');
define('NONCE_KEY',        '*EmD=50L$L+!iNU,+b^:-!Yz%W!6:#a~VOg|HNj$Xpim  |v!l(H@)*!5| |FbK!');
define('AUTH_SALT',        ':}~+|_9=A;Am7?E|2 -rhU}nzq+n^@_XdXOz6M;#m uHjQ|~$s)iQ7I?X{/;ZtKX');
define('SECURE_AUTH_SALT', 'dk!HQt/h>nQx}g`)Ry^4^*<x)-!c,]i+s}k+Au#V6a6#rPhD8)rRE6K+:OtdEiYt');
define('LOGGED_IN_SALT',   'czc@*G|.$3}w`+M4ZE^RJ_0Q &z_D.#T^{;vk`BlJj-q]?!<4KWD~n4kUfKTo|-E');
define('NONCE_SALT',       'k(rjJO._^5crhe:&W;qX-;RNpYt#&T|?*Jt#lSN@}+!QYaf*O3`##~AX7._,*5yk');
define( 'WP_CACHE_KEY_SALT', 'pN4R_k9QRq9}%)2[ar&,z-NlI]F,[6OZM9D<drZncxJX}$.Lrh:WBdzB1n}taYd/' );


/**#@-*/

/**
 * WordPress database table prefix.
 *
 * You can have multiple installations in one database if you give each
 * a unique prefix. Only numbers, letters, and underscores please!
 */
$table_prefix = 'wp_';

/**
 * For developers: WordPress debugging mode.
 *
 * Change this to true to enable the display of notices during development.
 * It is strongly recommended that plugin and theme developers use WP_DEBUG
 * in their development environments.
 *
 * For information on other constants that can be used for debugging,
 * visit the documentation.
 *
 * @link https://wordpress.org/support/article/debugging-in-wordpress/
 */
define( 'WP_DEBUG', false );
define( 'WP_DEBUG_DISPLAY', false );
define( 'WP_DEBUG_LOG', true );


//define('COOKIE_DOMAIN', 'staging-kindredsitebuilder.kinsta.cloud');
define('COOKIE_DOMAIN', $_SERVER['HTTP_HOST']);
define('MULTISITE', true);
define('SUBDOMAIN_INSTALL', false);
define('SITE_ID_CURRENT_SITE', 1);
define('BLOG_ID_CURRENT_SITE', 1);
define('WP_ALLOW_MULTISITE', true);
//define('DOMAIN_CURRENT_SITE', 'staging-kindredsitebuilder.kinsta.cloud');
define('DOMAIN_CURRENT_SITE', $_SERVER['HTTP_HOST']);
define('PATH_CURRENT_SITE', '/');
//define( 'SUNRISE', 'on' );
define('DISABLE_WP_CRON', 'true');
define('DISALLOW_FILE_MODS',false);
define('DISALLOW_UNFILTERED_HTML',true);
define('DISALLOW_FILE_EDIT',true);


define('JWT_AUTH_SECRET_KEY', 'DfeHV62vkfZoGEpVr68yP57DuWf0UiL2');

/* Add any custom values between this line and the "stop editing" line. */


define( 'WP_MEMORY_LIMIT', '256M' );


/* That's all, stop editing! Happy publishing. */

/** Absolute path to the WordPress directory. */
if ( ! defined( 'ABSPATH' ) ) {
	define( 'ABSPATH', __DIR__ . '/' );
}

/** Sets up WordPress vars and included files. */
require_once ABSPATH . 'wp-settings.php';